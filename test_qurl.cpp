#include <QtCore/QUrl>
#include <QtCore/QDebug>
#include <QtCore/QCoreApplication>

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);

    QString testPath = "/home/<USER>/Desktop/[  ]+=  ";
    qDebug() << "Original path:" << testPath;
    qDebug() << "Original path length:" << testPath.length();

    auto url = QUrl::fromUserInput(testPath);
    qDebug() << "URL:" << url;
    qDebug() << "URL toString:" << url.toString();

    if (url.isLocalFile()) {
        QString localFile = url.toLocalFile();
        qDebug() << "Local file:" << localFile;
        qDebug() << "Local file length:" << localFile.length();
        qDebug() << "Are they equal?" << (testPath == localFile);
    } else {
        qDebug() << "Not a local file";
    }

    return 0;
}
